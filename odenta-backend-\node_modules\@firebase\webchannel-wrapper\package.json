{"name": "@firebase/webchannel-wrapper", "version": "1.0.3", "description": "A wrapper of the webchannel packages from closure-library for use outside of a closure compiled application", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "empty.js", "exports": {"./webchannel-blob": {"types": "./dist/webchannel-blob/webchannel_blob_types.d.ts", "require": "./dist/webchannel-blob/webchannel_blob_es2018.js", "default": "./dist/webchannel-blob/esm/webchannel_blob_es2018.js"}, "./bloom-blob": {"types": "./dist/bloom-blob/bloom_blob_types.d.ts", "require": "./dist/bloom-blob/bloom_blob_es2018.js", "default": "./dist/bloom-blob/esm/bloom_blob_es2018.js"}, "./package.json": "./package.json"}, "files": ["dist", "bloom-blob/package.json", "webchannel-blob/package.json"], "scripts": {"dev": "watch 'yarn build' src", "build": "rollup -c", "test": "echo 'No test suite for webchannel-wrapper'", "test:ci": "echo 'No test suite for webchannel-wrapper'", "trusted-type-check": "tsec -p tsconfig.json --noEmit"}, "license": "Apache-2.0", "devDependencies": {"closure-net": "git+https://github.com/google/closure-net.git#0412666", "@rollup/plugin-commonjs": "21.1.0", "rollup": "2.79.1", "rollup-plugin-copy": "3.5.0", "rollup-plugin-sourcemaps": "0.6.3", "rollup-plugin-typescript2": "0.31.2", "typescript": "5.5.4"}, "repository": {"directory": "packages/webchannel-wrapper", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "typings": "./dist/webchannel-blob/webchannel_blob_types.d.ts", "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}}