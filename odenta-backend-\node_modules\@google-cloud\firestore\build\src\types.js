"use strict";
/*!
 * Copyright 2018 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultConverter = defaultConverter;
/**
 * A default converter to use when none is provided.
 *
 * By declaring the converter as a variable instead of creating the object
 * inside defaultConverter(), object equality when comparing default converters
 * is preserved.
 * @private
 * @internal
 */
const defaultConverterObj = {
    toFirestore(modelObject) {
        return modelObject;
    },
    fromFirestore(snapshot) {
        return snapshot.data();
    },
};
/**
 * A default converter to use when none is provided.
 * @private
 * @internal
 */
function defaultConverter() {
    return defaultConverterObj;
}
//# sourceMappingURL=types.js.map