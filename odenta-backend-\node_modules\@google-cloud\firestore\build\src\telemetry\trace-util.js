"use strict";
/**
 * Copyright 2024 Google LLC. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ATTRIBUTE_KEY_ATTEMPTS_REMAINING = exports.ATTRIBUTE_KEY_ATTEMPTS_ALLOWED = exports.ATTRIBUTE_KEY_TRANSACTION_TYPE = exports.ATTRIBUTE_KEY_IS_RETRY_WITH_CURSOR = exports.ATTRIBUTE_KEY_NUM_RESPONSES = exports.ATTRIBUTE_KEY_IS_TRANSACTIONAL = exports.ATTRIBUTE_KEY_DOC_COUNT = exports.ATTRIBUTE_SETTINGS_PREFIX = exports.ATTRIBUTE_SERVICE_PREFIX = exports.SPAN_NAME_BULK_WRITER_COMMIT = exports.SPAN_NAME_PARTITION_QUERY = exports.SPAN_NAME_BATCH_COMMIT = exports.SPAN_NAME_TRANSACTION_COMMIT = exports.SPAN_NAME_TRANSACTION_ROLLBACK = exports.SPAN_NAME_TRANSACTION_GET_DOCUMENTS = exports.SPAN_NAME_TRANSACTION_GET_DOCUMENT = exports.SPAN_NAME_TRANSACTION_GET_AGGREGATION_QUERY = exports.SPAN_NAME_TRANSACTION_GET_QUERY = exports.SPAN_NAME_TRANSACTION_RUN = exports.SPAN_NAME_AGGREGATION_QUERY_GET = exports.SPAN_NAME_QUERY_GET = exports.SPAN_NAME_COL_REF_LIST_DOCUMENTS = exports.SPAN_NAME_COL_REF_ADD = exports.SPAN_NAME_DOC_REF_LIST_COLLECTIONS = exports.SPAN_NAME_DOC_REF_GET = exports.SPAN_NAME_DOC_REF_DELETE = exports.SPAN_NAME_DOC_REF_UPDATE = exports.SPAN_NAME_DOC_REF_SET = exports.SPAN_NAME_DOC_REF_CREATE = exports.SPAN_NAME_RUN_AGGREGATION_QUERY = exports.SPAN_NAME_RUN_QUERY = exports.SPAN_NAME_BATCH_GET_DOCUMENTS = exports.SERVICE = void 0;
/**
 * Span names for instrumented operations.
 */
exports.SERVICE = 'google.firestore.v1.Firestore/';
exports.SPAN_NAME_BATCH_GET_DOCUMENTS = 'BatchGetDocuments';
exports.SPAN_NAME_RUN_QUERY = 'RunQuery';
exports.SPAN_NAME_RUN_AGGREGATION_QUERY = 'RunAggregationQuery';
exports.SPAN_NAME_DOC_REF_CREATE = 'DocumentReference.Create';
exports.SPAN_NAME_DOC_REF_SET = 'DocumentReference.Set';
exports.SPAN_NAME_DOC_REF_UPDATE = 'DocumentReference.Update';
exports.SPAN_NAME_DOC_REF_DELETE = 'DocumentReference.Delete';
exports.SPAN_NAME_DOC_REF_GET = 'DocumentReference.Get';
exports.SPAN_NAME_DOC_REF_LIST_COLLECTIONS = 'DocumentReference.ListCollections';
exports.SPAN_NAME_COL_REF_ADD = 'CollectionReference.Add';
exports.SPAN_NAME_COL_REF_LIST_DOCUMENTS = 'CollectionReference.ListDocuments';
exports.SPAN_NAME_QUERY_GET = 'Query.Get';
exports.SPAN_NAME_AGGREGATION_QUERY_GET = 'AggregationQuery.Get';
exports.SPAN_NAME_TRANSACTION_RUN = 'Transaction.Run';
exports.SPAN_NAME_TRANSACTION_GET_QUERY = 'Transaction.Get.Query';
exports.SPAN_NAME_TRANSACTION_GET_AGGREGATION_QUERY = 'Transaction.Get.AggregationQuery';
exports.SPAN_NAME_TRANSACTION_GET_DOCUMENT = 'Transaction.Get.Document';
exports.SPAN_NAME_TRANSACTION_GET_DOCUMENTS = 'Transaction.Get.Documents';
exports.SPAN_NAME_TRANSACTION_ROLLBACK = 'Transaction.Rollback';
exports.SPAN_NAME_TRANSACTION_COMMIT = 'Transaction.Commit';
exports.SPAN_NAME_BATCH_COMMIT = 'Batch.Commit';
exports.SPAN_NAME_PARTITION_QUERY = 'PartitionQuery';
exports.SPAN_NAME_BULK_WRITER_COMMIT = 'BulkWriter.Commit';
exports.ATTRIBUTE_SERVICE_PREFIX = 'gcp.firestore';
exports.ATTRIBUTE_SETTINGS_PREFIX = `${exports.ATTRIBUTE_SERVICE_PREFIX}.settings`;
exports.ATTRIBUTE_KEY_DOC_COUNT = 'doc_count';
exports.ATTRIBUTE_KEY_IS_TRANSACTIONAL = 'transactional';
exports.ATTRIBUTE_KEY_NUM_RESPONSES = 'response_count';
exports.ATTRIBUTE_KEY_IS_RETRY_WITH_CURSOR = 'retry_query_with_cursor';
exports.ATTRIBUTE_KEY_TRANSACTION_TYPE = 'transaction_type';
exports.ATTRIBUTE_KEY_ATTEMPTS_ALLOWED = 'attempts_allowed';
exports.ATTRIBUTE_KEY_ATTEMPTS_REMAINING = 'attempts_remaining';
//# sourceMappingURL=trace-util.js.map