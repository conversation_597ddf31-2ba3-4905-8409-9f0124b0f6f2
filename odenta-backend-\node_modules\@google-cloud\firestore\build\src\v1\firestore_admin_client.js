"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirestoreAdminClient = void 0;
const jsonProtos = require("../../protos/admin_v1.json");
/**
 * Client JSON configuration object, loaded from
 * `src/v1/firestore_admin_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./firestore_admin_client_config.json");
const version = require('../../../package.json').version;
/**
 *  The Cloud Firestore Admin API.
 *
 *  This API provides several administrative services for Cloud Firestore.
 *
 *  Project, Database, Namespace, Collection, Collection Group, and Document are
 *  used as defined in the Google Cloud Firestore API.
 *
 *  Operation: An Operation represents work being performed in the background.
 *
 *  The index service manages Cloud Firestore indexes.
 *
 *  Index creation is performed asynchronously.
 *  An Operation resource is created for each such asynchronous operation.
 *  The state of the operation (including any errors encountered)
 *  may be queried via the Operation resource.
 *
 *  The Operations collection provides a record of actions performed for the
 *  specified Project (including any Operations in progress). Operations are not
 *  created directly but through calls on other collections or resources.
 *
 *  An Operation that is done may be deleted so that it is no longer listed as
 *  part of the Operation collection. Operations are garbage collected after
 *  30 days. By default, ListOperations will only return in progress and failed
 *  operations. To list completed operation, issue a ListOperations request with
 *  the filter `done: true`.
 *
 *  Operations are created by service `FirestoreAdmin`, but are accessed via
 *  service `google.longrunning.Operations`.
 * @class
 * @memberof v1
 */
class FirestoreAdminClient {
    /**
     * Construct an instance of FirestoreAdminClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new FirestoreAdminClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        var _a, _b, _c, _d, _e;
        this._terminated = false;
        this.descriptors = {
            page: {},
            stream: {},
            longrunning: {},
            batching: {},
        };
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if ((opts === null || opts === void 0 ? void 0 : opts.universe_domain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universeDomain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== (opts === null || opts === void 0 ? void 0 : opts.universeDomain)) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            (_c = (_b = (_a = opts === null || opts === void 0 ? void 0 : opts.universeDomain) !== null && _a !== void 0 ? _a : opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== null && _b !== void 0 ? _b : universeDomainEnvVar) !== null && _c !== void 0 ? _c : 'googleapis.com';
        this._servicePath = 'firestore.' + this._universeDomain;
        const servicePath = (opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint) || this._servicePath;
        this._providedCustomServicePath = !!((opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint));
        const port = (opts === null || opts === void 0 ? void 0 : opts.port) || staticMembers.port;
        const clientConfig = (_d = opts === null || opts === void 0 ? void 0 : opts.clientConfig) !== null && _d !== void 0 ? _d : {};
        const fallback = (_e = opts === null || opts === void 0 ? void 0 : opts.fallback) !== null && _e !== void 0 ? _e : (typeof window !== 'undefined' && typeof (window === null || window === void 0 ? void 0 : window.fetch) === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            backupPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/backups/{backup}'),
            backupSchedulePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/databases/{database}/backupSchedules/{backup_schedule}'),
            collectionGroupPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/databases/{database}/collectionGroups/{collection}'),
            databasePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/databases/{database}'),
            fieldPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/databases/{database}/collectionGroups/{collection}/fields/{field}'),
            indexPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/databases/{database}/collectionGroups/{collection}/indexes/{index}'),
            locationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}'),
            projectPathTemplate: new this._gaxModule.PathTemplate('projects/{project}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listIndexes: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'indexes'),
            listFields: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'fields'),
        };
        const protoFilesRoot = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined,
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [
                {
                    selector: 'google.longrunning.Operations.CancelOperation',
                    post: '/v1/{name=projects/*/databases/*/operations/*}:cancel',
                    body: '*',
                },
                {
                    selector: 'google.longrunning.Operations.DeleteOperation',
                    delete: '/v1/{name=projects/*/databases/*/operations/*}',
                },
                {
                    selector: 'google.longrunning.Operations.GetOperation',
                    get: '/v1/{name=projects/*/databases/*/operations/*}',
                },
                {
                    selector: 'google.longrunning.Operations.ListOperations',
                    get: '/v1/{name=projects/*/databases/*}/operations',
                },
            ];
        }
        this.operationsClient = this._gaxModule
            .lro(lroOptions)
            .operationsClient(opts);
        const createIndexResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.Index');
        const createIndexMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.IndexOperationMetadata');
        const updateFieldResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.Field');
        const updateFieldMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.FieldOperationMetadata');
        const exportDocumentsResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.ExportDocumentsResponse');
        const exportDocumentsMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.ExportDocumentsMetadata');
        const importDocumentsResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const importDocumentsMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.ImportDocumentsMetadata');
        const bulkDeleteDocumentsResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.BulkDeleteDocumentsResponse');
        const bulkDeleteDocumentsMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.BulkDeleteDocumentsMetadata');
        const createDatabaseResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.Database');
        const createDatabaseMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.CreateDatabaseMetadata');
        const updateDatabaseResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.Database');
        const updateDatabaseMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.UpdateDatabaseMetadata');
        const deleteDatabaseResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.Database');
        const deleteDatabaseMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.DeleteDatabaseMetadata');
        const restoreDatabaseResponse = protoFilesRoot.lookup('.google.firestore.admin.v1.Database');
        const restoreDatabaseMetadata = protoFilesRoot.lookup('.google.firestore.admin.v1.RestoreDatabaseMetadata');
        this.descriptors.longrunning = {
            createIndex: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createIndexResponse.decode.bind(createIndexResponse), createIndexMetadata.decode.bind(createIndexMetadata)),
            updateField: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateFieldResponse.decode.bind(updateFieldResponse), updateFieldMetadata.decode.bind(updateFieldMetadata)),
            exportDocuments: new this._gaxModule.LongrunningDescriptor(this.operationsClient, exportDocumentsResponse.decode.bind(exportDocumentsResponse), exportDocumentsMetadata.decode.bind(exportDocumentsMetadata)),
            importDocuments: new this._gaxModule.LongrunningDescriptor(this.operationsClient, importDocumentsResponse.decode.bind(importDocumentsResponse), importDocumentsMetadata.decode.bind(importDocumentsMetadata)),
            bulkDeleteDocuments: new this._gaxModule.LongrunningDescriptor(this.operationsClient, bulkDeleteDocumentsResponse.decode.bind(bulkDeleteDocumentsResponse), bulkDeleteDocumentsMetadata.decode.bind(bulkDeleteDocumentsMetadata)),
            createDatabase: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createDatabaseResponse.decode.bind(createDatabaseResponse), createDatabaseMetadata.decode.bind(createDatabaseMetadata)),
            updateDatabase: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateDatabaseResponse.decode.bind(updateDatabaseResponse), updateDatabaseMetadata.decode.bind(updateDatabaseMetadata)),
            deleteDatabase: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteDatabaseResponse.decode.bind(deleteDatabaseResponse), deleteDatabaseMetadata.decode.bind(deleteDatabaseMetadata)),
            restoreDatabase: new this._gaxModule.LongrunningDescriptor(this.operationsClient, restoreDatabaseResponse.decode.bind(restoreDatabaseResponse), restoreDatabaseMetadata.decode.bind(restoreDatabaseMetadata)),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.firestore.admin.v1.FirestoreAdmin', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.firestoreAdminStub) {
            return this.firestoreAdminStub;
        }
        // Put together the "service stub" for
        // google.firestore.admin.v1.FirestoreAdmin.
        this.firestoreAdminStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.firestore.admin.v1.FirestoreAdmin')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.firestore.admin.v1.FirestoreAdmin, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const firestoreAdminStubMethods = [
            'createIndex',
            'listIndexes',
            'getIndex',
            'deleteIndex',
            'getField',
            'updateField',
            'listFields',
            'exportDocuments',
            'importDocuments',
            'bulkDeleteDocuments',
            'createDatabase',
            'getDatabase',
            'listDatabases',
            'updateDatabase',
            'deleteDatabase',
            'getBackup',
            'listBackups',
            'deleteBackup',
            'restoreDatabase',
            'createBackupSchedule',
            'getBackupSchedule',
            'listBackupSchedules',
            'updateBackupSchedule',
            'deleteBackupSchedule',
        ];
        for (const methodName of firestoreAdminStubMethods) {
            const callPromise = this.firestoreAdminStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.firestoreAdminStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'firestore.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'firestore.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/datastore',
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    getIndex(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getIndex(request, options, callback);
    }
    deleteIndex(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteIndex(request, options, callback);
    }
    getField(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getField(request, options, callback);
    }
    getDatabase(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getDatabase(request, options, callback);
    }
    listDatabases(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listDatabases(request, options, callback);
    }
    getBackup(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getBackup(request, options, callback);
    }
    listBackups(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listBackups(request, options, callback);
    }
    deleteBackup(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteBackup(request, options, callback);
    }
    createBackupSchedule(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createBackupSchedule(request, options, callback);
    }
    getBackupSchedule(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getBackupSchedule(request, options, callback);
    }
    listBackupSchedules(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listBackupSchedules(request, options, callback);
    }
    updateBackupSchedule(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'backup_schedule.name': (_a = request.backupSchedule.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateBackupSchedule(request, options, callback);
    }
    deleteBackupSchedule(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteBackupSchedule(request, options, callback);
    }
    createIndex(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createIndex(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createIndex()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.create_index.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_CreateIndex_async
     */
    async checkCreateIndexProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createIndex, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateField(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'field.name': (_a = request.field.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateField(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updateField()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.update_field.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_UpdateField_async
     */
    async checkUpdateFieldProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateField, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    exportDocuments(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.exportDocuments(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `exportDocuments()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.export_documents.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_ExportDocuments_async
     */
    async checkExportDocumentsProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.exportDocuments, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    importDocuments(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.importDocuments(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `importDocuments()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.import_documents.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_ImportDocuments_async
     */
    async checkImportDocumentsProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.importDocuments, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    bulkDeleteDocuments(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.bulkDeleteDocuments(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `bulkDeleteDocuments()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.bulk_delete_documents.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_BulkDeleteDocuments_async
     */
    async checkBulkDeleteDocumentsProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.bulkDeleteDocuments, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createDatabase(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createDatabase(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createDatabase()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.create_database.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_CreateDatabase_async
     */
    async checkCreateDatabaseProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createDatabase, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateDatabase(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'database.name': (_a = request.database.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateDatabase(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updateDatabase()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.update_database.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_UpdateDatabase_async
     */
    async checkUpdateDatabaseProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateDatabase, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteDatabase(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteDatabase(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `deleteDatabase()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.delete_database.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_DeleteDatabase_async
     */
    async checkDeleteDatabaseProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteDatabase, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    restoreDatabase(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.restoreDatabase(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `restoreDatabase()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.restore_database.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_RestoreDatabase_async
     */
    async checkRestoreDatabaseProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.restoreDatabase, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listIndexes(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listIndexes(request, options, callback);
    }
    /**
     * Equivalent to `listIndexes`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. A parent name of the form
     *   `projects/{project_id}/databases/{database_id}/collectionGroups/{collection_id}`
     * @param {string} request.filter
     *   The filter to apply to list results.
     * @param {number} request.pageSize
     *   The number of results to return.
     * @param {string} request.pageToken
     *   A page token, returned from a previous call to
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListIndexes|FirestoreAdmin.ListIndexes},
     *   that may be used to get the next page of results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.firestore.admin.v1.Index|Index} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listIndexesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listIndexesStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listIndexes'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listIndexes.createStream(this.innerApiCalls.listIndexes, request, callSettings);
    }
    /**
     * Equivalent to `listIndexes`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. A parent name of the form
     *   `projects/{project_id}/databases/{database_id}/collectionGroups/{collection_id}`
     * @param {string} request.filter
     *   The filter to apply to list results.
     * @param {number} request.pageSize
     *   The number of results to return.
     * @param {string} request.pageToken
     *   A page token, returned from a previous call to
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListIndexes|FirestoreAdmin.ListIndexes},
     *   that may be used to get the next page of results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.firestore.admin.v1.Index|Index}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.list_indexes.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_ListIndexes_async
     */
    listIndexesAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listIndexes'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listIndexes.asyncIterate(this.innerApiCalls['listIndexes'], request, callSettings);
    }
    listFields(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listFields(request, options, callback);
    }
    /**
     * Equivalent to `listFields`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. A parent name of the form
     *   `projects/{project_id}/databases/{database_id}/collectionGroups/{collection_id}`
     * @param {string} request.filter
     *   The filter to apply to list results. Currently,
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListFields|FirestoreAdmin.ListFields}
     *   only supports listing fields that have been explicitly overridden. To issue
     *   this query, call
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListFields|FirestoreAdmin.ListFields}
     *   with a filter that includes `indexConfig.usesAncestorConfig:false` or
     *   `ttlConfig:*`.
     * @param {number} request.pageSize
     *   The number of results to return.
     * @param {string} request.pageToken
     *   A page token, returned from a previous call to
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListFields|FirestoreAdmin.ListFields},
     *   that may be used to get the next page of results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.firestore.admin.v1.Field|Field} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listFieldsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFieldsStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listFields'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listFields.createStream(this.innerApiCalls.listFields, request, callSettings);
    }
    /**
     * Equivalent to `listFields`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. A parent name of the form
     *   `projects/{project_id}/databases/{database_id}/collectionGroups/{collection_id}`
     * @param {string} request.filter
     *   The filter to apply to list results. Currently,
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListFields|FirestoreAdmin.ListFields}
     *   only supports listing fields that have been explicitly overridden. To issue
     *   this query, call
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListFields|FirestoreAdmin.ListFields}
     *   with a filter that includes `indexConfig.usesAncestorConfig:false` or
     *   `ttlConfig:*`.
     * @param {number} request.pageSize
     *   The number of results to return.
     * @param {string} request.pageToken
     *   A page token, returned from a previous call to
     *   {@link protos.google.firestore.admin.v1.FirestoreAdmin.ListFields|FirestoreAdmin.ListFields},
     *   that may be used to get the next page of results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.firestore.admin.v1.Field|Field}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/firestore_admin.list_fields.js</caption>
     * region_tag:firestore_v1_generated_FirestoreAdmin_ListFields_async
     */
    listFieldsAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listFields'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listFields.asyncIterate(this.innerApiCalls['listFields'], request, callSettings);
    }
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.getOperation(request, options, callback);
    }
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request, options) {
        var _a;
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.listOperationsAsync(request, options);
    }
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.cancelOperation(request, options, callback);
    }
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.deleteOperation(request, options, callback);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified backup resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} backup
     * @returns {string} Resource name string.
     */
    backupPath(project, location, backup) {
        return this.pathTemplates.backupPathTemplate.render({
            project: project,
            location: location,
            backup: backup,
        });
    }
    /**
     * Parse the project from Backup resource.
     *
     * @param {string} backupName
     *   A fully-qualified path representing Backup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBackupName(backupName) {
        return this.pathTemplates.backupPathTemplate.match(backupName).project;
    }
    /**
     * Parse the location from Backup resource.
     *
     * @param {string} backupName
     *   A fully-qualified path representing Backup resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromBackupName(backupName) {
        return this.pathTemplates.backupPathTemplate.match(backupName).location;
    }
    /**
     * Parse the backup from Backup resource.
     *
     * @param {string} backupName
     *   A fully-qualified path representing Backup resource.
     * @returns {string} A string representing the backup.
     */
    matchBackupFromBackupName(backupName) {
        return this.pathTemplates.backupPathTemplate.match(backupName).backup;
    }
    /**
     * Return a fully-qualified backupSchedule resource name string.
     *
     * @param {string} project
     * @param {string} database
     * @param {string} backup_schedule
     * @returns {string} Resource name string.
     */
    backupSchedulePath(project, database, backupSchedule) {
        return this.pathTemplates.backupSchedulePathTemplate.render({
            project: project,
            database: database,
            backup_schedule: backupSchedule,
        });
    }
    /**
     * Parse the project from BackupSchedule resource.
     *
     * @param {string} backupScheduleName
     *   A fully-qualified path representing BackupSchedule resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBackupScheduleName(backupScheduleName) {
        return this.pathTemplates.backupSchedulePathTemplate.match(backupScheduleName).project;
    }
    /**
     * Parse the database from BackupSchedule resource.
     *
     * @param {string} backupScheduleName
     *   A fully-qualified path representing BackupSchedule resource.
     * @returns {string} A string representing the database.
     */
    matchDatabaseFromBackupScheduleName(backupScheduleName) {
        return this.pathTemplates.backupSchedulePathTemplate.match(backupScheduleName).database;
    }
    /**
     * Parse the backup_schedule from BackupSchedule resource.
     *
     * @param {string} backupScheduleName
     *   A fully-qualified path representing BackupSchedule resource.
     * @returns {string} A string representing the backup_schedule.
     */
    matchBackupScheduleFromBackupScheduleName(backupScheduleName) {
        return this.pathTemplates.backupSchedulePathTemplate.match(backupScheduleName).backup_schedule;
    }
    /**
     * Return a fully-qualified collectionGroup resource name string.
     *
     * @param {string} project
     * @param {string} database
     * @param {string} collection
     * @returns {string} Resource name string.
     */
    collectionGroupPath(project, database, collection) {
        return this.pathTemplates.collectionGroupPathTemplate.render({
            project: project,
            database: database,
            collection: collection,
        });
    }
    /**
     * Parse the project from CollectionGroup resource.
     *
     * @param {string} collectionGroupName
     *   A fully-qualified path representing CollectionGroup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCollectionGroupName(collectionGroupName) {
        return this.pathTemplates.collectionGroupPathTemplate.match(collectionGroupName).project;
    }
    /**
     * Parse the database from CollectionGroup resource.
     *
     * @param {string} collectionGroupName
     *   A fully-qualified path representing CollectionGroup resource.
     * @returns {string} A string representing the database.
     */
    matchDatabaseFromCollectionGroupName(collectionGroupName) {
        return this.pathTemplates.collectionGroupPathTemplate.match(collectionGroupName).database;
    }
    /**
     * Parse the collection from CollectionGroup resource.
     *
     * @param {string} collectionGroupName
     *   A fully-qualified path representing CollectionGroup resource.
     * @returns {string} A string representing the collection.
     */
    matchCollectionFromCollectionGroupName(collectionGroupName) {
        return this.pathTemplates.collectionGroupPathTemplate.match(collectionGroupName).collection;
    }
    /**
     * Return a fully-qualified database resource name string.
     *
     * @param {string} project
     * @param {string} database
     * @returns {string} Resource name string.
     */
    databasePath(project, database) {
        return this.pathTemplates.databasePathTemplate.render({
            project: project,
            database: database,
        });
    }
    /**
     * Parse the project from Database resource.
     *
     * @param {string} databaseName
     *   A fully-qualified path representing Database resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatabaseName(databaseName) {
        return this.pathTemplates.databasePathTemplate.match(databaseName).project;
    }
    /**
     * Parse the database from Database resource.
     *
     * @param {string} databaseName
     *   A fully-qualified path representing Database resource.
     * @returns {string} A string representing the database.
     */
    matchDatabaseFromDatabaseName(databaseName) {
        return this.pathTemplates.databasePathTemplate.match(databaseName).database;
    }
    /**
     * Return a fully-qualified field resource name string.
     *
     * @param {string} project
     * @param {string} database
     * @param {string} collection
     * @param {string} field
     * @returns {string} Resource name string.
     */
    fieldPath(project, database, collection, field) {
        return this.pathTemplates.fieldPathTemplate.render({
            project: project,
            database: database,
            collection: collection,
            field: field,
        });
    }
    /**
     * Parse the project from Field resource.
     *
     * @param {string} fieldName
     *   A fully-qualified path representing Field resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFieldName(fieldName) {
        return this.pathTemplates.fieldPathTemplate.match(fieldName).project;
    }
    /**
     * Parse the database from Field resource.
     *
     * @param {string} fieldName
     *   A fully-qualified path representing Field resource.
     * @returns {string} A string representing the database.
     */
    matchDatabaseFromFieldName(fieldName) {
        return this.pathTemplates.fieldPathTemplate.match(fieldName).database;
    }
    /**
     * Parse the collection from Field resource.
     *
     * @param {string} fieldName
     *   A fully-qualified path representing Field resource.
     * @returns {string} A string representing the collection.
     */
    matchCollectionFromFieldName(fieldName) {
        return this.pathTemplates.fieldPathTemplate.match(fieldName).collection;
    }
    /**
     * Parse the field from Field resource.
     *
     * @param {string} fieldName
     *   A fully-qualified path representing Field resource.
     * @returns {string} A string representing the field.
     */
    matchFieldFromFieldName(fieldName) {
        return this.pathTemplates.fieldPathTemplate.match(fieldName).field;
    }
    /**
     * Return a fully-qualified index resource name string.
     *
     * @param {string} project
     * @param {string} database
     * @param {string} collection
     * @param {string} index
     * @returns {string} Resource name string.
     */
    indexPath(project, database, collection, index) {
        return this.pathTemplates.indexPathTemplate.render({
            project: project,
            database: database,
            collection: collection,
            index: index,
        });
    }
    /**
     * Parse the project from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).project;
    }
    /**
     * Parse the database from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the database.
     */
    matchDatabaseFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).database;
    }
    /**
     * Parse the collection from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the collection.
     */
    matchCollectionFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).collection;
    }
    /**
     * Parse the index from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the index.
     */
    matchIndexFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).index;
    }
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project, location) {
        return this.pathTemplates.locationPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).project;
    }
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).location;
    }
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project) {
        return this.pathTemplates.projectPathTemplate.render({
            project: project,
        });
    }
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName) {
        return this.pathTemplates.projectPathTemplate.match(projectName).project;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.firestoreAdminStub && !this._terminated) {
            return this.firestoreAdminStub.then(stub => {
                this._terminated = true;
                stub.close();
                this.locationsClient.close();
                this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.FirestoreAdminClient = FirestoreAdminClient;
//# sourceMappingURL=firestore_admin_client.js.map