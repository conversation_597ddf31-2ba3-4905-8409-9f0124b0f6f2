{"name": "@google-cloud/paginator", "version": "5.0.2", "description": "A result paging utility used by Google node.js modules", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "googleapis/nodejs-paginator", "scripts": {"test": "c8 mocha build/test", "compile": "tsc -p .", "fix": "gts fix", "prelint": "cd samples; npm link ../; npm install", "lint": "gts check", "prepare": "npm run compile", "pretest": "npm run compile", "docs": "compodoc src/", "presystem-test": "npm run compile", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "clean": "gts clean", "precompile": "gts clean"}, "keywords": [], "files": ["build/src", "!build/src/**/*.map"], "author": "Google Inc.", "license": "Apache-2.0", "devDependencies": {"@compodoc/compodoc": "1.1.23", "@types/extend": "^3.0.0", "@types/mocha": "^9.0.0", "@types/node": "^20.4.9", "@types/proxyquire": "^1.3.28", "@types/sinon": "^17.0.0", "@types/uuid": "^9.0.0", "c8": "^9.0.0", "codecov": "^3.0.4", "gts": "^5.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "proxyquire": "^2.0.1", "sinon": "^17.0.0", "typescript": "^5.1.6", "uuid": "^9.0.0"}, "dependencies": {"arrify": "^2.0.0", "extend": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}